# Sinoair Agent 开发详细设计文档

## 1. 项目概述

### 1.1 项目背景
Sinoair Agent是一个基于LLM大模型的国际航空货运代理智能识别与回填系统。系统通过识别各类航空货运单证（图片、PDF等），提取结构化数据，并自动回填到目标业务系统中。

### 1.2 技术架构
- **后端**: Java 17 + Spring Boot 3.5.0 + MySQL 8.x + Redis
- **前端**: Vue 3.5.16 + TypeScript 5.8.3 + Element Plus 2.9.11
- **LLM集成**: 支持OpenAI、Anthropic、百度、阿里等多种大模型
- **自动化**: Puppeteer/Playwright 实现网页自动化回填

## 2. 核心功能模块开发设计

### 2.1 LLM访问统一设计

#### 2.1.1 设计原则
**统一入口设计模式：**
- 采用策略模式和工厂模式，实现统一的LLM访问接口
- 所有大模型调用使用相同的入参和出参格式
- 不同大模型提供商使用不同的实现类，但对外接口保持一致
- 支持动态扩展新的大模型提供商，无需修改现有代码

#### 2.1.2 核心接口设计

**LLM统一调用接口：**
```java
public interface LlmService {
    /**
     * 统一LLM调用接口
     * @param request 统一请求参数
     * @return 统一响应结果
     */
    LlmResponse call(LlmRequest request);

    /**
     * 流式调用接口
     * @param request 统一请求参数
     * @param callback 流式回调
     */
    void callStream(LlmRequest request, StreamCallback callback);

    /**
     * 检查模型可用性
     * @return 可用性状态
     */
    boolean isAvailable();

    /**
     * 获取模型信息
     * @return 模型详细信息
     */
    ModelInfo getModelInfo();
}
```

**统一请求参数：**
```java
@Data
@Builder
public class LlmRequest {
    // 基础参数
    private String modelCode;           // 模型编码
    private List<Message> messages;     // 消息列表
    private String systemPrompt;        // 系统提示词

    // 生成参数
    private Integer maxTokens;          // 最大token数
    private Double temperature;         // 温度参数
    private Double topP;               // top_p参数
    private Double frequencyPenalty;   // 频率惩罚
    private Double presencePenalty;    // 存在惩罚
    private List<String> stopWords;    // 停止词

    // 功能参数
    private boolean enableVision;       // 是否启用视觉功能
    private boolean enableFunctionCall; // 是否启用函数调用
    private List<FunctionDefinition> functions; // 函数定义列表

    // 扩展参数
    private Map<String, Object> extraParams; // 扩展参数

    // 请求元数据
    private String requestId;          // 请求ID
    private String userId;             // 用户ID
    private Long timestamp;            // 请求时间戳
}

@Data
public class Message {
    private String role;               // 角色：system/user/assistant
    private String content;            // 文本内容
    private List<ContentPart> parts;   // 多模态内容
}

@Data
public class ContentPart {
    private String type;               // 类型：text/image/file
    private String content;            // 内容
    private Map<String, Object> metadata; // 元数据
}
```

**统一响应结果：**
```java
@Data
@Builder
public class LlmResponse {
    // 响应内容
    private String content;            // 生成的文本内容
    private List<Message> messages;    // 完整对话消息
    private List<FunctionCall> functionCalls; // 函数调用结果

    // 使用统计
    private TokenUsage tokenUsage;     // token使用统计
    private Long processingTime;       // 处理耗时(毫秒)
    private Double cost;               // 调用成本

    // 质量指标
    private Double confidenceScore;    // 置信度分数
    private String finishReason;       // 结束原因

    // 响应元数据
    private String requestId;          // 请求ID
    private String modelVersion;       // 模型版本
    private Long timestamp;            // 响应时间戳
    private boolean success;           // 是否成功
    private String errorCode;          // 错误码
    private String errorMessage;       // 错误信息

    // 扩展信息
    private Map<String, Object> metadata; // 扩展元数据
}

@Data
public class TokenUsage {
    private Integer promptTokens;      // 输入token数
    private Integer completionTokens;  // 输出token数
    private Integer totalTokens;       // 总token数
}
```

#### 2.1.3 工厂模式实现

**LLM服务工厂：**
```java
@Component
public class LlmServiceFactory {

    private final Map<String, LlmService> serviceMap = new ConcurrentHashMap<>();

    @Autowired
    private List<LlmService> llmServices;

    @PostConstruct
    public void init() {
        // 注册所有LLM服务实现
        llmServices.forEach(service -> {
            ModelInfo modelInfo = service.getModelInfo();
            serviceMap.put(modelInfo.getProvider(), service);
        });
    }

    /**
     * 根据模型编码获取对应的LLM服务
     */
    public LlmService getLlmService(String modelCode) {
        LlmModel model = llmModelService.getByCode(modelCode);
        if (model == null) {
            throw new BusinessException("模型不存在: " + modelCode);
        }

        LlmService service = serviceMap.get(model.getProvider());
        if (service == null) {
            throw new BusinessException("不支持的模型提供商: " + model.getProvider());
        }

        return service;
    }

    /**
     * 获取所有可用的LLM服务
     */
    public List<LlmService> getAllServices() {
        return new ArrayList<>(serviceMap.values());
    }
}
```

#### 2.1.4 具体实现类设计

**OpenAI实现类：**
```java
@Service
@ConditionalOnProperty(name = "llm.openai.enabled", havingValue = "true")
public class OpenAiLlmService implements LlmService {

    @Autowired
    private OpenAiClient openAiClient;

    @Override
    public LlmResponse call(LlmRequest request) {
        try {
            // 转换请求参数
            ChatCompletionRequest openAiRequest = convertToOpenAiRequest(request);

            // 调用OpenAI API
            ChatCompletionResponse openAiResponse = openAiClient.createChatCompletion(openAiRequest);

            // 转换响应结果
            return convertToLlmResponse(openAiResponse, request);

        } catch (Exception e) {
            return LlmResponse.builder()
                .success(false)
                .errorCode("OPENAI_API_ERROR")
                .errorMessage(e.getMessage())
                .requestId(request.getRequestId())
                .timestamp(System.currentTimeMillis())
                .build();
        }
    }

    @Override
    public void callStream(LlmRequest request, StreamCallback callback) {
        // 流式调用实现
        ChatCompletionRequest openAiRequest = convertToOpenAiRequest(request);
        openAiRequest.setStream(true);

        openAiClient.createChatCompletionStream(openAiRequest, new OpenAiStreamCallback() {
            @Override
            public void onMessage(ChatCompletionChunk chunk) {
                // 转换并回调
                callback.onMessage(convertChunkToMessage(chunk));
            }

            @Override
            public void onError(Throwable error) {
                callback.onError(error);
            }

            @Override
            public void onComplete() {
                callback.onComplete();
            }
        });
    }

    @Override
    public boolean isAvailable() {
        try {
            // 发送测试请求检查可用性
            return openAiClient.checkHealth();
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public ModelInfo getModelInfo() {
        return ModelInfo.builder()
            .provider("openai")
            .supportedModels(Arrays.asList("gpt-4", "gpt-3.5-turbo"))
            .supportsVision(true)
            .supportsFunctionCall(true)
            .supportsStream(true)
            .build();
    }

    // 私有方法：请求参数转换
    private ChatCompletionRequest convertToOpenAiRequest(LlmRequest request) {
        // 实现参数转换逻辑
    }

    // 私有方法：响应结果转换
    private LlmResponse convertToLlmResponse(ChatCompletionResponse response, LlmRequest request) {
        // 实现响应转换逻辑
    }
}
```

**百度文心实现类：**
```java
@Service
@ConditionalOnProperty(name = "llm.baidu.enabled", havingValue = "true")
public class BaiduLlmService implements LlmService {

    @Autowired
    private BaiduClient baiduClient;

    @Override
    public LlmResponse call(LlmRequest request) {
        try {
            // 转换请求参数
            ErnieRequest ernieRequest = convertToBaiduRequest(request);

            // 调用百度API
            ErnieResponse ernieResponse = baiduClient.chat(ernieRequest);

            // 转换响应结果
            return convertToLlmResponse(ernieResponse, request);

        } catch (Exception e) {
            return LlmResponse.builder()
                .success(false)
                .errorCode("BAIDU_API_ERROR")
                .errorMessage(e.getMessage())
                .requestId(request.getRequestId())
                .timestamp(System.currentTimeMillis())
                .build();
        }
    }

    @Override
    public ModelInfo getModelInfo() {
        return ModelInfo.builder()
            .provider("baidu")
            .supportedModels(Arrays.asList("ernie-4.0", "ernie-3.5"))
            .supportsVision(true)
            .supportsFunctionCall(false)
            .supportsStream(true)
            .build();
    }

    // 其他方法实现...
}
```

**阿里通义千问实现类：**
```java
@Service
@ConditionalOnProperty(name = "llm.alibaba.enabled", havingValue = "true")
public class AlibabaLlmService implements LlmService {

    @Autowired
    private DashScopeClient dashScopeClient;

    @Override
    public LlmResponse call(LlmRequest request) {
        try {
            // 转换请求参数
            GenerationRequest aliRequest = convertToAlibabaRequest(request);

            // 调用阿里API
            GenerationResponse aliResponse = dashScopeClient.call(aliRequest);

            // 转换响应结果
            return convertToLlmResponse(aliResponse, request);

        } catch (Exception e) {
            return LlmResponse.builder()
                .success(false)
                .errorCode("ALIBABA_API_ERROR")
                .errorMessage(e.getMessage())
                .requestId(request.getRequestId())
                .timestamp(System.currentTimeMillis())
                .build();
        }
    }

    @Override
    public ModelInfo getModelInfo() {
        return ModelInfo.builder()
            .provider("alibaba")
            .supportedModels(Arrays.asList("qwen-max", "qwen-plus", "qwen-turbo"))
            .supportsVision(true)
            .supportsFunctionCall(true)
            .supportsStream(true)
            .build();
    }

    // 其他方法实现...
}
```

#### 2.1.5 统一调用服务

**LLM统一调用服务：**
```java
@Service
public class UnifiedLlmService {

    @Autowired
    private LlmServiceFactory llmServiceFactory;

    @Autowired
    private LlmModelService llmModelService;

    @Autowired
    private LlmCallLogService llmCallLogService;

    /**
     * 统一LLM调用入口
     */
    public LlmResponse call(String modelCode, LlmRequest request) {
        // 设置模型编码
        request.setModelCode(modelCode);
        request.setRequestId(generateRequestId());
        request.setTimestamp(System.currentTimeMillis());

        // 获取模型配置
        LlmModel model = llmModelService.getByCode(modelCode);
        if (model == null || !"active".equals(model.getStatus())) {
            throw new BusinessException("模型不可用: " + modelCode);
        }

        // 应用模型默认配置
        applyModelDefaults(request, model);

        // 获取对应的LLM服务
        LlmService llmService = llmServiceFactory.getLlmService(modelCode);

        // 记录调用开始
        LlmCallLog callLog = createCallLog(request, model);

        try {
            // 执行调用
            LlmResponse response = llmService.call(request);

            // 更新调用记录
            updateCallLog(callLog, response, true);

            return response;

        } catch (Exception e) {
            // 更新调用记录
            LlmResponse errorResponse = LlmResponse.builder()
                .success(false)
                .errorCode("CALL_FAILED")
                .errorMessage(e.getMessage())
                .requestId(request.getRequestId())
                .timestamp(System.currentTimeMillis())
                .build();

            updateCallLog(callLog, errorResponse, false);

            throw new BusinessException("LLM调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 流式调用
     */
    public void callStream(String modelCode, LlmRequest request, StreamCallback callback) {
        request.setModelCode(modelCode);
        request.setRequestId(generateRequestId());
        request.setTimestamp(System.currentTimeMillis());

        LlmModel model = llmModelService.getByCode(modelCode);
        applyModelDefaults(request, model);

        LlmService llmService = llmServiceFactory.getLlmService(modelCode);

        // 包装回调以记录日志
        StreamCallback wrappedCallback = new StreamCallback() {
            private final LlmCallLog callLog = createCallLog(request, model);
            private final StringBuilder contentBuilder = new StringBuilder();

            @Override
            public void onMessage(String message) {
                contentBuilder.append(message);
                callback.onMessage(message);
            }

            @Override
            public void onError(Throwable error) {
                LlmResponse errorResponse = LlmResponse.builder()
                    .success(false)
                    .errorMessage(error.getMessage())
                    .requestId(request.getRequestId())
                    .build();
                updateCallLog(callLog, errorResponse, false);
                callback.onError(error);
            }

            @Override
            public void onComplete() {
                LlmResponse response = LlmResponse.builder()
                    .success(true)
                    .content(contentBuilder.toString())
                    .requestId(request.getRequestId())
                    .timestamp(System.currentTimeMillis())
                    .build();
                updateCallLog(callLog, response, true);
                callback.onComplete();
            }
        };

        llmService.callStream(request, wrappedCallback);
    }

    /**
     * 批量调用
     */
    public List<LlmResponse> batchCall(String modelCode, List<LlmRequest> requests) {
        return requests.parallelStream()
            .map(request -> call(modelCode, request))
            .collect(Collectors.toList());
    }

    // 私有方法实现...
    private void applyModelDefaults(LlmRequest request, LlmModel model) {
        if (request.getMaxTokens() == null) {
            request.setMaxTokens(model.getMaxTokens());
        }
        if (request.getTemperature() == null) {
            request.setTemperature(model.getTemperature());
        }
        if (request.getTopP() == null) {
            request.setTopP(model.getTopP());
        }
    }

    private String generateRequestId() {
        return "llm_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
}
```

#### 2.1.6 配置管理

**LLM配置管理：**
```yaml
# application.yml
llm:
  # 全局配置
  global:
    timeout: 30000              # 超时时间(毫秒)
    retry-count: 3              # 重试次数
    enable-cache: true          # 是否启用缓存
    cache-ttl: 3600             # 缓存TTL(秒)

  # OpenAI配置
  openai:
    enabled: true
    api-key: ${OPENAI_API_KEY}
    base-url: https://api.openai.com/v1
    organization: ${OPENAI_ORG_ID:}
    timeout: 60000

  # 百度配置
  baidu:
    enabled: true
    api-key: ${BAIDU_API_KEY}
    secret-key: ${BAIDU_SECRET_KEY}
    base-url: https://aip.baidubce.com

  # 阿里配置
  alibaba:
    enabled: true
    api-key: ${ALIBABA_API_KEY}
    base-url: https://dashscope.aliyuncs.com/api/v1

  # Anthropic配置
  anthropic:
    enabled: false
    api-key: ${ANTHROPIC_API_KEY}
    base-url: https://api.anthropic.com
```

#### 2.1.7 扩展机制设计

**新增LLM提供商步骤：**

1. **实现LlmService接口**
```java
@Service
@ConditionalOnProperty(name = "llm.newprovider.enabled", havingValue = "true")
public class NewProviderLlmService implements LlmService {
    // 实现所有接口方法
}
```

2. **添加配置项**
```yaml
llm:
  newprovider:
    enabled: true
    api-key: ${NEW_PROVIDER_API_KEY}
    base-url: https://api.newprovider.com
```

3. **注册到数据库**
```sql
INSERT INTO llm_models (
    name, code, provider, model_version, api_endpoint,
    max_tokens, temperature, top_p, supports_vision,
    supports_function_call, status, created_at
) VALUES (
    'New Provider Model', 'newprovider-v1', 'newprovider', 'v1.0',
    'https://api.newprovider.com/v1/chat', 4096, 0.7, 0.9,
    true, false, 'active', NOW()
);
```

4. **自动发现和注册**
Spring Boot会自动发现新的LlmService实现并注册到工厂中，无需修改现有代码。

#### 2.1.8 数据库设计扩展

**LLM调用日志表：**
```sql
CREATE TABLE llm_call_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    request_id VARCHAR(64) NOT NULL UNIQUE COMMENT '请求ID',
    user_id BIGINT COMMENT '用户ID',
    model_code VARCHAR(50) NOT NULL COMMENT '模型编码',
    provider VARCHAR(50) NOT NULL COMMENT '提供商',

    -- 请求信息
    prompt_tokens INT COMMENT '输入token数',
    max_tokens INT COMMENT '最大token数',
    temperature DECIMAL(3,2) COMMENT '温度参数',

    -- 响应信息
    completion_tokens INT COMMENT '输出token数',
    total_tokens INT COMMENT '总token数',
    processing_time BIGINT COMMENT '处理耗时(毫秒)',
    cost DECIMAL(10,6) COMMENT '调用成本',

    -- 状态信息
    success BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否成功',
    error_code VARCHAR(50) COMMENT '错误码',
    error_message TEXT COMMENT '错误信息',

    -- 时间信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_user_model_created (user_id, model_code, created_at),
    INDEX idx_provider_created (provider, created_at),
    INDEX idx_request_id (request_id)
) COMMENT 'LLM调用日志表';
```

### 2.2 LLM大模型管理模块

#### 2.2.1 列表查询功能
**开发说明：**
- 实现LLM模型的分页查询和条件筛选
- 支持按名称、提供商、状态等条件过滤
- 显示模型基本信息、状态、创建时间等

**数据流转：**
1. 前端发送查询请求到 `/api/llm-models`
2. 后端 `LlmModelController.list()` 接收请求
3. 调用 `LlmModelService.getPageList()` 查询数据
4. 从 `llm_models` 表获取数据并分页返回
5. 前端接收数据并渲染列表

**数据变更说明：**
- 查询操作，不涉及数据变更
- 记录查询日志到 `operation_logs` 表

**字段变更明细：**
```sql
-- 查询字段
SELECT id, name, code, provider, model_version, status,
       supports_vision, supports_function_call, created_at
FROM llm_models
WHERE status = ? AND name LIKE ?
ORDER BY created_at DESC
```

#### 2.2.2 新增LLM功能
**开发说明：**
- 提供LLM模型信息录入表单
- 验证模型配置的有效性
- 支持API连接测试

**数据流转：**
1. 前端提交表单数据到 `/api/llm-models`
2. 后端 `LlmModelController.create()` 接收数据
3. 数据校验：名称唯一性、API配置有效性
4. 调用 `LlmModelService.create()` 创建记录
5. 插入数据到 `llm_models` 表
6. 返回创建结果

**数据变更说明：**
- 新增记录到 `llm_models` 表
- 记录操作日志到 `operation_logs` 表

**字段变更明细：**
```sql
-- 插入新记录
INSERT INTO llm_models (
    name, code, provider, model_version, api_endpoint,
    max_tokens, temperature, top_p, supports_vision,
    supports_function_call, cost_per_1k_tokens, status,
    description, created_at, updated_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())
```

#### 2.2.3 编辑LLM功能
**开发说明：**
- 支持修改LLM模型配置信息
- 保留历史版本记录
- 更新时验证配置有效性

**数据流转：**
1. 前端获取模型详情 `/api/llm-models/{id}`
2. 用户修改后提交到 `/api/llm-models/{id}`
3. 后端验证数据有效性
4. 更新 `llm_models` 表记录
5. 返回更新结果

**数据变更说明：**
- 更新 `llm_models` 表中对应记录
- `updated_at` 字段更新为当前时间
- 记录操作日志

**字段变更明细：**
```sql
-- 更新记录
UPDATE llm_models SET
    name = ?, provider = ?, model_version = ?,
    api_endpoint = ?, max_tokens = ?, temperature = ?,
    top_p = ?, description = ?, updated_at = NOW()
WHERE id = ?
```

### 2.3 回填网站维护模块

#### 2.3.1 列表查询功能
**开发说明：**
- 显示已配置的回填网站列表
- 支持按状态、创建时间等筛选
- 显示网站连接状态和最后测试时间

**数据流转：**
1. 前端请求 `/api/websites` 获取列表
2. 后端查询 `websites` 表数据
3. 关联查询创建者信息
4. 返回分页数据

**字段变更明细：**
```sql
-- 查询网站列表
SELECT w.id, w.name, w.code, w.url, w.status,
       w.success_rate, w.last_test_at, w.created_at,
       u.real_name as creator_name
FROM websites w
LEFT JOIN users u ON w.creator_id = u.id
WHERE w.status = ?
ORDER BY w.created_at DESC
```

#### 2.3.2 新增回填网站功能
**开发说明：**
- 配置目标网站基本信息
- 设置认证方式和登录配置
- 配置页面元素选择器
- 支持连接测试验证

**数据流转：**
1. 前端提交网站配置到 `/api/websites`
2. 后端验证URL有效性和认证配置
3. 测试网站连接可用性
4. 保存配置到 `websites` 表
5. 保存详细配置到 `website_configs` 表

**数据变更说明：**
- 新增记录到 `websites` 表
- 新增配置项到 `website_configs` 表
- 记录操作日志

**字段变更明细：**
```sql
-- 插入网站记录
INSERT INTO websites (
    name, code, url, description, auth_type,
    auth_config, selectors, status, creator_id,
    created_at, updated_at
) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())

-- 插入配置项
INSERT INTO website_configs (
    website_id, config_key, config_value, config_type,
    description, created_at
) VALUES (?, ?, ?, ?, ?, NOW())
```

#### 2.3.3 编辑回填网站功能
**开发说明：**
- 修改网站配置信息
- 更新认证和选择器配置
- 重新测试连接有效性

**数据变更说明：**
- 更新 `websites` 表记录
- 更新或新增 `website_configs` 表配置项
- 更新 `updated_at` 时间戳

### 2.4 Agent管理模块

#### 2.4.1 列表查询功能
**开发说明：**
- 显示用户创建的Agent列表
- 支持按分类、状态筛选
- 显示使用统计和成功率

**数据流转：**
1. 前端请求 `/api/agents` 获取列表
2. 后端根据用户权限查询数据
3. 关联查询分类和LLM模型信息
4. 计算使用统计数据

**字段变更明细：**
```sql
-- 查询Agent列表
SELECT a.id, a.name, a.description, a.version, a.status,
       a.usage_count, a.success_rate, a.created_at,
       c.name as category_name, l.name as llm_model_name
FROM agents a
LEFT JOIN agent_categories c ON a.category_id = c.id
LEFT JOIN llm_models l ON a.llm_model_id = l.id
WHERE a.creator_id = ? AND a.status != 'deleted'
ORDER BY a.updated_at DESC
```

#### 2.4.2 Agent维护功能
**开发说明：**
- 创建和编辑Agent基本信息
- 配置系统提示词和用户提示词模板
- 定义输出JSON格式
- 设置验证规则

**数据流转：**
1. 创建：前端提交到 `/api/agents`
2. 编辑：前端提交到 `/api/agents/{id}`
3. 后端验证提示词格式和输出模式
4. 保存到 `agents` 表
5. 创建版本记录到 `agent_versions` 表

**数据变更说明：**
- 新增/更新 `agents` 表记录
- 新增版本记录到 `agent_versions` 表
- 更新使用统计信息

**字段变更明细：**
```sql
-- 插入Agent记录
INSERT INTO agents (
    name, description, category_id, creator_id, llm_model_id,
    system_prompt, user_prompt_template, output_schema,
    validation_rules, config, status, version,
    created_at, updated_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'draft', '1.0.0', NOW(), NOW())

-- 插入版本记录
INSERT INTO agent_versions (
    agent_id, version, system_prompt, user_prompt_template,
    output_schema, validation_rules, config, change_log,
    created_by, created_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
```

#### 2.4.3 提示词调试功能
**开发说明：**
- 提供在线提示词编辑器
- 支持测试文件上传和实时调试
- 显示LLM响应结果和性能指标
- 支持提示词版本对比

**数据流转：**
1. 前端上传测试文件到 `/api/agents/{id}/test`
2. 后端调用对应LLM模型进行测试
3. 记录测试结果到 `agent_tests` 表
4. 返回测试结果和性能指标

**数据变更说明：**
- 新增测试记录到 `agent_tests` 表
- 更新Agent的性能统计数据
- 记录调试日志

**字段变更明细：**
```sql
-- 插入测试记录
INSERT INTO agent_tests (
    agent_id, test_name, test_file_path, test_file_name,
    expected_output, actual_output, test_result,
    accuracy_score, processing_time, error_message,
    test_by, created_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())

-- 更新Agent统计
UPDATE agents SET
    usage_count = usage_count + 1,
    success_rate = (
        SELECT AVG(CASE WHEN test_result = 'passed' THEN 100 ELSE 0 END)
        FROM agent_tests WHERE agent_id = ?
    ),
    avg_processing_time = (
        SELECT AVG(processing_time) FROM agent_tests WHERE agent_id = ?
    ),
    updated_at = NOW()
WHERE id = ?
```

#### 2.4.4 调试记录查看功能
**开发说明：**
- 显示Agent的历史调试记录
- 支持按时间、结果状态筛选
- 提供详细的输入输出对比

**数据流转：**
1. 前端请求 `/api/agents/{id}/test-records`
2. 后端查询 `agent_tests` 表数据
3. 返回分页的测试记录

**字段变更明细：**
```sql
-- 查询测试记录
SELECT id, test_name, test_file_name, test_result,
       accuracy_score, processing_time, created_at,
       u.real_name as tester_name
FROM agent_tests at
LEFT JOIN users u ON at.test_by = u.id
WHERE at.agent_id = ?
ORDER BY at.created_at DESC
```

#### 2.4.5 调试记录的提示词回滚功能
**开发说明：**
- 支持回滚到历史版本的提示词
- 创建新的版本记录
- 更新Agent当前配置

**数据流转：**
1. 前端选择历史版本并请求回滚
2. 后端从 `agent_versions` 表获取历史配置
3. 更新 `agents` 表当前配置
4. 创建新的版本记录

**数据变更说明：**
- 更新 `agents` 表的提示词配置
- 新增版本记录到 `agent_versions` 表
- 更新版本号

**字段变更明细：**
```sql
-- 获取历史版本
SELECT system_prompt, user_prompt_template, output_schema,
       validation_rules, config
FROM agent_versions
WHERE agent_id = ? AND version = ?

-- 更新Agent配置
UPDATE agents SET
    system_prompt = ?, user_prompt_template = ?,
    output_schema = ?, validation_rules = ?, config = ?,
    version = ?, updated_at = NOW()
WHERE id = ?

-- 创建新版本记录
INSERT INTO agent_versions (
    agent_id, version, system_prompt, user_prompt_template,
    output_schema, validation_rules, config,
    change_log, created_by, created_at
) VALUES (?, ?, ?, ?, ?, ?, ?, '回滚到版本 ?', ?, NOW())
```

### 2.5 Agent使用模块

#### 2.5.1 Agent选择功能
**开发说明：**
- 显示可用的Agent列表
- 按分类筛选Agent
- 显示Agent的适用场景和说明

**数据流转：**
1. 前端请求 `/api/agents/available`
2. 后端查询状态为'published'的Agent
3. 返回Agent基本信息和分类

**字段变更明细：**
```sql
-- 查询可用Agent
SELECT a.id, a.name, a.description, a.version,
       a.usage_count, a.success_rate,
       c.name as category_name, c.icon as category_icon
FROM agents a
LEFT JOIN agent_categories c ON a.category_id = c.id
WHERE a.status = 'published'
ORDER BY a.usage_count DESC, a.success_rate DESC
```

#### 2.5.2 文件上传处理功能
**开发说明：**
- 支持多种文件格式上传（PDF、图片等）
- 文件格式验证和大小限制
- 调用选定的Agent进行处理
- 实时显示处理进度

**数据流转：**
1. 前端上传文件到 `/api/files/upload`
2. 后端保存文件并创建处理记录
3. 异步调用Agent处理文件
4. 更新处理状态和结果

**数据变更说明：**
- 新增记录到 `processing_records` 表
- 创建处理批次记录到 `processing_batches` 表
- 记录处理日志到 `processing_logs` 表

**字段变更明细：**
```sql
-- 创建处理记录
INSERT INTO processing_records (
    user_id, agent_id, file_name, file_path, file_size,
    file_type, file_hash, mime_type, status, created_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())

-- 创建处理批次
INSERT INTO processing_batches (
    batch_name, user_id, agent_id, total_files,
    status, created_at
) VALUES (?, ?, ?, ?, 'pending', NOW())

-- 更新处理结果
UPDATE processing_records SET
    status = 'completed', output_data = ?, raw_response = ?,
    processing_time = ?, confidence_score = ?, token_usage = ?,
    completed_at = NOW()
WHERE id = ?

-- 记录处理步骤
INSERT INTO processing_logs (
    processing_record_id, step_name, step_order, status,
    start_time, end_time, duration, input_data, output_data,
    error_message, created_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
```

#### 2.5.3 结果查看编辑功能
**开发说明：**
- 显示LLM识别的结构化结果
- 支持用户手动编辑和校正
- 提供结果验证和格式检查
- 支持结果导出

**数据流转：**
1. 前端请求 `/api/processing-records/{id}/result`
2. 后端返回处理结果JSON数据
3. 用户编辑后提交更新
4. 更新处理记录的输出数据

**数据变更说明：**
- 更新 `processing_records` 表的 `output_data` 字段
- 记录用户编辑操作日志

**字段变更明细：**
```sql
-- 获取处理结果
SELECT id, file_name, file_path, output_data, raw_response,
       confidence_score, processing_time, status, created_at
FROM processing_records
WHERE id = ? AND user_id = ?

-- 更新编辑后的结果
UPDATE processing_records SET
    output_data = ?, updated_at = NOW()
WHERE id = ? AND user_id = ?

-- 记录编辑日志
INSERT INTO operation_logs (
    user_id, operation, request_params, ip_address,
    status, created_at
) VALUES (?, 'edit_processing_result', ?, ?, 'success', NOW())
```

#### 2.5.4 回填操作功能
**开发说明：**
- 选择目标回填网站
- 映射字段到网站表单
- 执行自动化回填操作
- 监控回填结果和异常

**数据流转：**
1. 前端选择网站并提交回填请求
2. 后端创建回填任务记录
3. 调用自动化服务执行回填
4. 更新回填结果和状态

**数据变更说明：**
- 新增记录到 `fillback_records` 表
- 记录回填步骤到 `fillback_logs` 表
- 更新网站成功率统计

**字段变更明细：**
```sql
-- 创建回填记录
INSERT INTO fillback_records (
    processing_record_id, website_id, status, fillback_data,
    browser_type, created_at
) VALUES (?, ?, 'pending', ?, 'chromium', NOW())

-- 更新回填结果
UPDATE fillback_records SET
    status = 'completed', execution_result = ?, execution_time = ?,
    screenshot_path = ?, video_path = ?, completed_at = NOW()
WHERE id = ?

-- 记录回填步骤
INSERT INTO fillback_logs (
    fillback_record_id, step_name, step_order, action_type,
    target_selector, action_data, status, start_time,
    end_time, duration, screenshot_path, created_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())

-- 更新网站统计
UPDATE websites SET
    success_rate = (
        SELECT AVG(CASE WHEN status = 'completed' THEN 100 ELSE 0 END)
        FROM fillback_records WHERE website_id = ?
    ),
    avg_execution_time = (
        SELECT AVG(execution_time)
        FROM fillback_records
        WHERE website_id = ? AND status = 'completed'
    ),
    updated_at = NOW()
WHERE id = ?
```

## 3. 数据状态管理

### 3.1 处理记录状态流转
```
pending -> processing -> completed/failed
```
- **pending**: 文件已上传，等待处理
- **processing**: 正在调用LLM进行识别
- **completed**: 处理完成，已生成结果
- **failed**: 处理失败，记录错误信息

### 3.2 Agent状态流转
```
draft -> testing -> published -> archived
```
- **draft**: 草稿状态，正在编辑配置
- **testing**: 测试状态，进行调试验证
- **published**: 已发布，可供用户使用
- **archived**: 已归档，不再使用

### 3.3 回填记录状态流转
```
pending -> processing -> completed/failed
```
- **pending**: 等待执行回填操作
- **processing**: 正在执行自动化回填
- **completed**: 回填成功完成
- **failed**: 回填失败，记录错误原因

### 3.4 网站连接状态
```
active -> testing -> inactive
```
- **active**: 网站配置正常，可用于回填
- **testing**: 正在测试连接状态
- **inactive**: 网站不可用或配置错误

## 4. 异常处理机制

### 4.1 文件处理异常
**异常类型及处理：**
- **文件格式不支持**:
  - 错误码: `FILE_FORMAT_NOT_SUPPORTED`
  - 处理: 返回支持的格式列表，提示用户重新上传
- **文件大小超限**:
  - 错误码: `FILE_SIZE_EXCEEDED`
  - 处理: 返回最大文件大小限制，建议压缩文件
- **LLM调用失败**:
  - 错误码: `LLM_API_ERROR`
  - 处理: 记录详细错误日志，支持自动重试机制
- **处理超时**:
  - 错误码: `PROCESSING_TIMEOUT`
  - 处理: 设置超时机制（默认5分钟），自动标记失败状态

**重试机制：**
```sql
-- 更新重试次数
UPDATE processing_records SET
    retry_count = retry_count + 1,
    error_message = ?,
    updated_at = NOW()
WHERE id = ? AND retry_count < 3
```

### 4.2 回填操作异常
**异常类型及处理：**
- **网站连接失败**:
  - 错误码: `WEBSITE_CONNECTION_FAILED`
  - 处理: 检查网站URL和网络连接，记录详细错误信息
- **认证失败**:
  - 错误码: `AUTHENTICATION_FAILED`
  - 处理: 提示重新配置认证信息，检查用户名密码
- **元素定位失败**:
  - 错误码: `ELEMENT_NOT_FOUND`
  - 处理: 提供详细的选择器错误信息，建议更新配置
- **数据格式错误**:
  - 错误码: `DATA_FORMAT_ERROR`
  - 处理: 验证数据格式，提供格式修正建议

**错误恢复策略：**
```sql
-- 记录回填错误
UPDATE fillback_records SET
    status = 'failed',
    error_code = ?,
    error_message = ?,
    retry_count = retry_count + 1,
    completed_at = NOW()
WHERE id = ?
```

### 4.3 系统级异常
- **数据库连接异常**: 使用连接池重连机制
- **Redis缓存异常**: 降级到数据库查询
- **文件存储异常**: 多存储策略备份

## 5. 性能优化策略

### 5.1 数据库优化
**索引优化：**
```sql
-- 处理记录查询优化
CREATE INDEX idx_processing_user_status_created
ON processing_records(user_id, status, created_at);

-- Agent查询优化
CREATE INDEX idx_agents_creator_status_updated
ON agents(creator_id, status, updated_at);

-- 回填记录查询优化
CREATE INDEX idx_fillback_website_status_created
ON fillback_records(website_id, status, created_at);
```

**分页查询优化：**
- 使用游标分页替代OFFSET分页
- 限制单页最大记录数（默认50条）
- 实现查询结果缓存

**数据清理策略：**
```sql
-- 定期清理过期日志（保留90天）
DELETE FROM operation_logs
WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 清理失败的处理记录（保留30天）
DELETE FROM processing_records
WHERE status = 'failed' AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 5.2 文件处理优化
**异步处理机制：**
- 使用消息队列（Redis）管理处理任务
- 实现任务优先级调度
- 支持批量文件并行处理

**缓存策略：**
```java
// 处理结果缓存
@Cacheable(value = "processing_results", key = "#fileHash")
public ProcessingResult getProcessingResult(String fileHash) {
    // 查询处理结果
}

// LLM响应缓存
@Cacheable(value = "llm_responses", key = "#prompt + #fileHash")
public LlmResponse callLlm(String prompt, String fileHash) {
    // 调用LLM
}
```

**文件存储优化：**
- 实现文件去重（基于文件哈希）
- 使用CDN加速文件访问
- 定期清理临时文件

### 5.3 前端性能优化
**组件优化：**
- 使用虚拟滚动处理大列表
- 实现组件懒加载和代码分割
- 优化图片和文件预览组件

**网络优化：**
- 实现请求防抖和节流
- 使用WebSocket实现实时状态更新
- 压缩API响应数据

**缓存策略：**
```typescript
// 前端缓存配置
const cacheConfig = {
  agents: { ttl: 300000 }, // 5分钟
  websites: { ttl: 600000 }, // 10分钟
  processingResults: { ttl: 1800000 } // 30分钟
};
```

## 6. 安全设计

### 6.1 数据安全
**敏感信息加密：**
```java
// LLM API密钥加密存储
@Component
public class EncryptionService {
    public String encryptApiKey(String apiKey) {
        // 使用AES加密
    }

    public String decryptApiKey(String encryptedKey) {
        // 解密API密钥
    }
}
```

**文件安全检查：**
- 文件类型白名单验证
- 文件内容病毒扫描
- 文件大小和数量限制
- 上传文件隔离存储

**数据脱敏：**
```sql
-- 日志中敏感数据脱敏
UPDATE operation_logs SET
    request_params = REGEXP_REPLACE(request_params,
        '"password":"[^"]*"', '"password":"***"')
WHERE operation = 'login';
```

### 6.2 访问控制
**API权限控制：**
```java
@PreAuthorize("hasRole('ADMIN') or @securityService.isOwner(#agentId)")
public Agent updateAgent(Long agentId, AgentUpdateRequest request) {
    // 更新Agent
}
```

**数据权限控制：**
- 用户只能访问自己创建的Agent和处理记录
- 管理员可以访问所有数据
- 实现行级数据权限控制

### 6.3 操作审计
**操作日志记录：**
```java
@Aspect
@Component
public class OperationLogAspect {
    @Around("@annotation(OperationLog)")
    public Object logOperation(ProceedingJoinPoint point) {
        // 记录操作日志
    }
}
```

**关键操作确认：**
- Agent发布需要二次确认
- 批量删除操作需要验证码
- 敏感配置修改需要管理员审批

### 6.4 系统安全
**防护机制：**
- 实现API访问频率限制
- 防止SQL注入和XSS攻击
- 使用HTTPS加密传输
- 定期安全漏洞扫描

**备份恢复：**
- 数据库定时备份（每日全量，每小时增量）
- 文件存储多地备份
- 配置信息版本控制
- 灾难恢复预案

## 7. 监控和运维

### 7.1 系统监控
**性能指标监控：**
- API响应时间和成功率
- 数据库连接池状态
- 文件处理队列长度
- LLM调用成功率和耗时

**业务指标监控：**
- 用户活跃度统计
- Agent使用频率排行
- 处理成功率趋势
- 回填操作成功率

### 7.2 告警机制
**告警规则：**
- API响应时间超过5秒
- 处理失败率超过10%
- 系统错误日志异常增长
- 存储空间使用率超过80%

**告警通知：**
- 邮件通知关键人员
- 短信通知紧急故障
- 系统内消息推送
- 集成企业微信/钉钉

这份开发详细设计文档涵盖了Sinoair Agent系统的核心功能模块开发要求，包括详细的数据流转、状态管理、异常处理、性能优化和安全设计等方面，为开发团队提供了全面的技术实施指导。