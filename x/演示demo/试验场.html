<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sinoair Agent - 试验场</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
        }
        
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            border-bottom: 1px solid #e4e7ed;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 1000;
        }
        
        .logo {
            font-size: 20px;
            font-weight: bold;
            color: #303133;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .search-box {
            width: 200px;
        }
        
        .sidebar {
            width: 200px;
            background: white;
            border-right: 1px solid #e4e7ed;
            margin-top: 60px;
            height: calc(100vh - 60px);
        }
        
        .main-content {
            flex: 1;
            margin-top: 60px;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .content-wrapper {
            background: white;
            border-radius: 8px;
            padding: 20px;
            height: calc(100vh - 100px);
        }
        
        .page-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #303133;
        }
        
        .workspace {
            display: flex;
            gap: 20px;
            height: calc(100% - 60px);
        }
        
        .left-panel {
            width: 300px;
        }
        
        .agent-section {
            margin-bottom: 20px;
        }
        
        .section-title {
            font-size: 14px;
            color: #606266;
            margin-bottom: 10px;
        }
        
        .agent-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .agent-item {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            color: #409eff;
            text-align: center;
        }
        
        .agent-item:hover {
            border-color: #409eff;
            background: #ecf5ff;
        }
        
        .work-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border: 2px dashed #dcdfe6;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .upload-area {
            text-align: center;
            padding: 40px;
        }
        
        .upload-icon {
            font-size: 48px;
            color: #409eff;
            margin-bottom: 16px;
        }
        
        .upload-text {
            font-size: 16px;
            color: #606266;
            margin-bottom: 8px;
        }
        
        .upload-hint {
            font-size: 12px;
            color: #909399;
        }
        
        .right-panel {
            width: 300px;
        }
        
        .result-section {
            height: 100%;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 16px;
            background: #fafafa;
        }
        
        .result-content {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #303133;
            white-space: pre-wrap;
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
            margin-top: 20px;
            justify-content: center;
        }
        
        .menu-item {
            padding: 12px 16px;
            cursor: pointer;
            color: #606266;
            border-left: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            background: #f5f7fa;
            color: #409eff;
        }
        
        .menu-item.active {
            background: #ecf5ff;
            color: #409eff;
            border-left-color: #409eff;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- 顶部导航 -->
            <div class="header">
                <div class="logo">Sinoair Agent</div>
                <div class="header-right">
                    <el-input 
                        v-model="searchText" 
                        placeholder="请输入内容" 
                        class="search-box"
                        :prefix-icon="Search">
                    </el-input>
                    <el-avatar :size="32" :src="avatarUrl">大雄</el-avatar>
                </div>
            </div>
            
            <!-- 左侧菜单 -->
            <div class="sidebar">
                <div class="menu-item active">试验场</div>
                <div class="menu-item">agent管理</div>
                <div class="menu-item">大模型管理</div>
                <div class="menu-item">回调网站管理</div>
                <div class="menu-item">用户管理</div>
                <div class="menu-item">日志查看</div>
                <div class="menu-item">统计报告</div>
            </div>
            
            <!-- 主内容区 -->
            <div class="main-content">
                <div class="content-wrapper">
                    <div class="page-title">试验场</div>
                    
                    <div class="workspace">
                        <!-- 左侧Agent选择 -->
                        <div class="left-panel">
                            <div class="agent-section">
                                <div class="section-title">Agent</div>
                                <div class="agent-list">
                                    <div class="agent-item" @click="selectAgent('AGENT-A')">AGENT-A</div>
                                    <div class="agent-item" @click="selectAgent('AGENT-B')">AGENT-B</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 中间工作区 -->
                        <div class="work-area">
                            <div class="section-title">工作区</div>
                            <div class="upload-area">
                                <div class="upload-icon">📄</div>
                                <div class="upload-text">点击或将文件拖拽到这里上传</div>
                                <div class="upload-hint">支持格式：.pdf .jpg...</div>
                            </div>
                            
                            <div class="action-buttons">
                                <el-button type="primary" size="large" @click="handleAI">AI处理</el-button>
                                <el-button size="large" @click="handleDataFill">数据回填</el-button>
                            </div>
                        </div>
                        
                        <!-- 右侧处理结果 -->
                        <div class="right-panel">
                            <div class="section-title">处理结果</div>
                            <div class="result-section">
                                <div class="result-content">{{ resultText }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;
        
        createApp({
            data() {
                return {
                    searchText: '',
                    selectedAgent: '',
                    resultText: '{\n  "A": "A1"\n}',
                    avatarUrl: ''
                }
            },
            methods: {
                selectAgent(agent) {
                    this.selectedAgent = agent;
                    ElMessage.success(`已选择 ${agent}`);
                },
                handleAI() {
                    if (!this.selectedAgent) {
                        ElMessage.warning('请先选择一个Agent');
                        return;
                    }
                    ElMessage.success('AI处理中...');
                    // 模拟处理结果
                    setTimeout(() => {
                        this.resultText = '{\n  "status": "processing",\n  "agent": "' + this.selectedAgent + '",\n  "result": "处理完成"\n}';
                    }, 1000);
                },
                handleDataFill() {
                    ElMessage.success('数据回填中...');
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
