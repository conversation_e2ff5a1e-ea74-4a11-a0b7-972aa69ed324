<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sinoair Agent - Agent管理</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
        }
        
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            border-bottom: 1px solid #e4e7ed;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 1000;
        }
        
        .logo {
            font-size: 20px;
            font-weight: bold;
            color: #303133;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .search-box {
            width: 200px;
        }
        
        .sidebar {
            width: 200px;
            background: white;
            border-right: 1px solid #e4e7ed;
            margin-top: 60px;
            height: calc(100vh - 60px);
        }
        
        .main-content {
            flex: 1;
            margin-top: 60px;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .content-wrapper {
            background: white;
            border-radius: 8px;
            padding: 20px;
            height: calc(100vh - 100px);
        }
        
        .page-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #303133;
        }
        
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .menu-item {
            padding: 12px 16px;
            cursor: pointer;
            color: #606266;
            border-left: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            background: #f5f7fa;
            color: #409eff;
        }
        
        .menu-item.active {
            background: #ecf5ff;
            color: #409eff;
            border-left-color: #409eff;
        }
        
        .status-tag {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
        }
        
        .status-online {
            background: #f0f9ff;
            color: #67c23a;
            border: 1px solid #b3e19d;
        }
        
        .status-testing {
            background: #fef0e6;
            color: #e6a23c;
            border: 1px solid #f5dab1;
        }
        
        .action-link {
            color: #409eff;
            cursor: pointer;
            text-decoration: none;
            margin-right: 12px;
        }
        
        .action-link:hover {
            text-decoration: underline;
        }
        
        .drawer-content {
            padding: 20px;
        }
        
        .drawer-section {
            margin-bottom: 24px;
        }
        
        .drawer-section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 12px;
            color: #303133;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 16px;
        }
        
        .form-item {
            flex: 1;
        }
        
        .upload-area {
            border: 2px dashed #dcdfe6;
            border-radius: 6px;
            padding: 40px;
            text-align: center;
            background: #fafafa;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        
        .upload-area:hover {
            border-color: #409eff;
        }
        
        .upload-icon {
            font-size: 32px;
            color: #409eff;
            margin-bottom: 12px;
        }
        
        .history-table {
            margin-top: 16px;
        }
        
        .drawer-footer {
            position: absolute;
            bottom: 0;
            right: 0;
            left: 0;
            padding: 16px 20px;
            background: white;
            border-top: 1px solid #e4e7ed;
            text-align: right;
        }
        
        .temperature-slider {
            margin: 20px 0;
        }
        
        .file-preview {
            width: 200px;
            height: 200px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #909399;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- 顶部导航 -->
            <div class="header">
                <div class="logo">Sinoair Agent</div>
                <div class="header-right">
                    <el-input 
                        v-model="searchText" 
                        placeholder="请输入内容" 
                        class="search-box"
                        :prefix-icon="Search">
                    </el-input>
                    <el-avatar :size="32" :src="avatarUrl">大雄</el-avatar>
                </div>
            </div>
            
            <!-- 左侧菜单 -->
            <div class="sidebar">
                <div class="menu-item" @click="goToPage('试验场')">试验场</div>
                <div class="menu-item active">agent管理</div>
                <div class="menu-item">大模型管理</div>
                <div class="menu-item">回调网站管理</div>
                <div class="menu-item">用户管理</div>
                <div class="menu-item">日志查看</div>
                <div class="menu-item">统计报告</div>
            </div>
            
            <!-- 主内容区 -->
            <div class="main-content">
                <div class="content-wrapper">
                    <div class="page-title">agent管理</div>
                    
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="toolbar-left">
                            <span>关键字</span>
                            <el-input v-model="keyword" placeholder="请输入内容" style="width: 200px;"></el-input>
                            <el-button type="primary">查询</el-button>
                        </div>
                        <el-button type="primary" @click="showCreateDialog">+ 创建</el-button>
                    </div>
                    
                    <!-- 数据表格 -->
                    <el-table :data="tableData" style="width: 100%">
                        <el-table-column prop="id" label="序号" width="80"></el-table-column>
                        <el-table-column prop="name" label="Agent名称" width="120"></el-table-column>
                        <el-table-column prop="code" label="Agent代码" width="120"></el-table-column>
                        <el-table-column prop="status" label="状态" width="100">
                            <template #default="scope">
                                <span :class="['status-tag', scope.row.status === '已发布' ? 'status-online' : 'status-testing']">
                                    {{ scope.row.status }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="creator" label="创建人" width="100"></el-table-column>
                        <el-table-column prop="createTime" label="创建时间" width="150"></el-table-column>
                        <el-table-column label="操作" width="200">
                            <template #default="scope">
                                <a class="action-link" @click="showHistoryDialog(scope.row)">📝 查看&编辑</a>
                                <a class="action-link" @click="showDebugDrawer(scope.row)">🔧 状态&调试</a>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="100">
                            <template #default="scope">
                                <a class="action-link" @click="deleteAgent(scope.row)">删除</a>
                            </template>
                        </el-table-column>
                    </el-table>
                    
                    <!-- 分页 -->
                    <div style="text-align: center; margin-top: 20px;">
                        <el-pagination
                            v-model:current-page="currentPage"
                            :page-size="pageSize"
                            :total="total"
                            layout="prev, pager, next"
                            @current-change="handlePageChange">
                        </el-pagination>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 创建Agent弹窗 -->
        <el-dialog v-model="createDialogVisible" title="创建Agent" width="600px">
            <el-form :model="createForm" label-width="100px">
                <div class="form-row">
                    <div class="form-item">
                        <el-form-item label="Agent代码">
                            <el-input v-model="createForm.code" placeholder="请输入内容"></el-input>
                        </el-form-item>
                    </div>
                    <div class="form-item">
                        <el-form-item label="Agent名称">
                            <el-input v-model="createForm.name" placeholder="请输入内容"></el-input>
                        </el-form-item>
                    </div>
                </div>
                <el-form-item label="提示词">
                    <el-input v-model="createForm.prompt" type="textarea" :rows="8" placeholder="请输入内容"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="createDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="createAgent">确认</el-button>
            </template>
        </el-dialog>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;
        
        createApp({
            data() {
                return {
                    searchText: '',
                    keyword: '',
                    currentPage: 1,
                    pageSize: 10,
                    total: 20,
                    avatarUrl: '',
                    createDialogVisible: false,
                    debugDrawerVisible: false,
                    historyDialogVisible: false,
                    createForm: {
                        code: '',
                        name: '',
                        prompt: ''
                    },
                    tableData: [
                        {
                            id: 1,
                            name: '智能体1',
                            code: 'X-AGENT-1',
                            status: '已发布',
                            creator: '大雄',
                            createTime: '时间1'
                        },
                        {
                            id: 2,
                            name: '智能体2',
                            code: 'X-AGENT-2',
                            status: '测试',
                            creator: '大雄',
                            createTime: '时间2'
                        }
                    ]
                }
            },
            methods: {
                goToPage(page) {
                    if (page === '试验场') {
                        window.location.href = '试验场.html';
                    }
                },
                showCreateDialog() {
                    this.createDialogVisible = true;
                },
                createAgent() {
                    ElMessage.success('创建成功');
                    this.createDialogVisible = false;
                },
                showHistoryDialog(row) {
                    ElMessage.info('查看编辑功能');
                },
                showDebugDrawer(row) {
                    ElMessage.info('调试功能');
                },
                deleteAgent(row) {
                    ElMessage.warning('删除功能');
                },
                handlePageChange(page) {
                    this.currentPage = page;
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
